import { Icons } from '@/assets/icons';
import { HeaderStyle } from '@/components/HeaderStyle';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { Avatar } from '@/components/ui/Avatar';
import { Spacer } from '@/components/Spacer';
import { View, TouchableOpacity, Share } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import * as Clipboard from 'expo-clipboard';
import { useGetInfiniteReferralFriendsQuery } from '@/apis/referral';
import { useToggleFollowUserMutation } from '@/apis/user/mutations';
import { IconLoading } from '@/components/IconLoading';
import { toastError, toastSuccess } from '@/utils/toast';
import type { IReferralFriend, IGetReferralFriendsResponse } from '@/apis/referral/types';
import type { InfiniteData } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';
import { useGetRefLink } from '@/hooks/useGetRefLink';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';
import { useCallback, useState } from 'react';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { Empty } from '@/components/Empty';

const YourReferralScreen = () => {
  const { onScroll, scrollY, headerHeight, onHeaderLayout } = useHeaderStyleAnimated();
  const { styles, theme } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const { refLink, refCode } = useGetRefLink();
  const [followingUserId, setFollowingUserId] = useState<number | null>(null);
  const {
    data: friendsData,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetInfiniteReferralFriendsQuery({ limit: 20 });

  const { mutateAsync: toggleFollowMutation } = useToggleFollowUserMutation();

  const friends =
    (friendsData as InfiniteData<IGetReferralFriendsResponse> | undefined)?.pages?.flatMap((page) => page.data) || [];
  const totalFriends =
    (friendsData as InfiniteData<IGetReferralFriendsResponse> | undefined)?.pages?.[0]?.meta?.totalItems || 0;

  const handleCopyReferral = useCallback(async () => {
    toastSuccess({ description: 'Copied!' });
    await Clipboard.setStringAsync(refCode);
  }, [refCode]);

  const handleOpenShare = useCallback(() => {
    Share.share({
      message: `Join me on Rabid to discuss, rate, and track your podcast activity!\n👉 Try it here: ${refLink}`,
    });
  }, [refLink]);

  const handleFollow = async (userId: number, isFollowed: boolean) => {
    try {
      setFollowingUserId(userId);
      await toggleFollowMutation(userId);
      toastSuccess({
        description: isFollowed ? 'Unfollowed this user successfully' : 'Followed this user successfully',
      });
      // queryClient.invalidateQueries({ queryKey: queryKeys.referral.friends() });
    } catch (error) {
      toastError(error);
    } finally {
      setFollowingUserId(null);
    }
  };

  const renderFriend = ({ item }: { item: IReferralFriend }) => (
    <View style={styles.friendItem}>
      <Avatar image={item.avatar} size={48} />

      <ThemedText type='defaultMedium' style={styles.friendName}>
        {item.username}
      </ThemedText>

      <CustomButton
        activeOpacity={1}
        type={item.isFollowed ? 'primaryOpacity10' : 'primary'}
        textType='tinySemiBold'
        style={styles.followBtn}
        disabled={followingUserId === item.userId}
        onPress={() => handleFollow(item.userId, item.isFollowed)}
      >
        {item.isFollowed ? 'Following' : 'Follow'}
      </CustomButton>
    </View>
  );

  const renderFooter = () => {
    return isFetchingNextPage ? <IconLoading /> : null;
  };

  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const renderHeaderReferral = useCallback(
    () => (
      <View>
        <View style={styles.shareSection}>
          <ThemedText type='subtitle' style={styles.shareTitle}>
            Share Rabid with Friends
          </ThemedText>
          <Spacer height={16} />
          <ThemedText type='small' style={styles.shareDesc}>
            Invite your friends and get inspired, track your favorite podcasts and build the community together.
          </ThemedText>
          <Spacer height={28} />
          <View style={styles.referralBox}>
            <View>
              <ThemedText type='tinyMedium'>Referral Code</ThemedText>
              <Spacer height={4} />
              <ThemedText type='defaultBold'>{refCode}</ThemedText>
            </View>
            <TouchableOpacity style={styles.copyBtn} onPress={handleCopyReferral} activeOpacity={0.7}>
              <Icons.Copy size={24} color={theme.colors.primary} />
              <Spacer width={8} />
              <ThemedText type='smallSemiBold' style={styles.copyText}>
                Copy
              </ThemedText>
            </TouchableOpacity>
          </View>
          <Spacer height={28} />
          <CustomButton textType='defaultBold' style={styles.inviteBtn} onPress={handleOpenShare}>
            Invite Friends
          </CustomButton>
        </View>

        <View style={styles.friendsSection}>
          <View style={styles.friendsHeader}>
            <ThemedText type='subtitleSemiBold' style={styles.friendsLabel}>
              Total Friends Using Your Code
            </ThemedText>
            <View style={styles.friendsCountBox}>
              <ThemedText type='title' style={styles.friendsCount}>
                {totalFriends}
              </ThemedText>
              <ThemedText type='tinySemiBold' style={styles.friendsCountText}>
                {totalFriends > 1 ? 'Friends' : 'Friend'}
              </ThemedText>
            </View>
          </View>

          <Spacer height={24} />
        </View>
      </View>
    ),
    [handleCopyReferral, handleOpenShare, refCode, styles, theme.colors.primary, totalFriends]
  );

  const renderSpacer = useCallback(() => <Spacer height={24} />, []);

  return (
    <View style={styles.container}>
      <HeaderStyle title='Your Referral' scrollY={scrollY} onHeaderLayout={onHeaderLayout} />

      <FlashListAnimate
        data={friends}
        onScroll={onScroll}
        ListHeaderComponent={renderHeaderReferral}
        renderItem={renderFriend}
        keyExtractor={(item) => item.userId.toString()}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListFooterComponent={renderFooter}
        style={styles.scrollView}
        ItemSeparatorComponent={renderSpacer}
        contentContainerStyle={[styles.contentList, { paddingTop: headerHeight + 40 }]}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Empty
              type='referral'
              emptyText='No friends have signed up using your referral code yet. Invite your friends to join using your referral code!'
              textType='defaultSemiBold'
              // imageStyle={{ width: 328, height: 282, marginHorizontal: 'auto' }}
              // containerStyle={{ paddingTop: 16 }}
            />
          </View>
        }
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
  scrollView: {
    flex: 1,
  },
  contentList: {
    minHeight: rt.screen.height,
    paddingHorizontal: 24,
    paddingBottom: rt.insets.bottom + 24,
  },
  shareSection: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.whiteOpacity10,
    paddingBottom: 32,
    marginBottom: 32,
  },
  shareTitle: {
    color: theme.colors.neutralWhite,
    lineHeight: 32,
  },
  shareDesc: {
    color: theme.colors.neutralWhite,
  },
  referralBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  copyBtn: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  copyText: {
    color: theme.colors.primary,
  },
  inviteBtn: {
    marginTop: 12,
    borderRadius: 9999,
    backgroundColor: theme.colors.primary,
    minHeight: 48,
    width: 320,
    margin: 'auto',
  },
  friendsSection: {
    // paddingBottom: 100,
  },
  friendsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  friendsLabel: {
    color: theme.colors.neutralWhite,
    width: 218,
    lineHeight: 24,
  },
  friendsCountBox: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: theme.colors.primaryOpacity10,
    borderRadius: 999,
    padding: 16,
  },
  friendsCount: {
    color: theme.colors.primary,
    marginRight: 8,
    // lineHeight: 24,
  },
  friendsCountText: {
    color: theme.colors.primary,
    // marginBottom: -2,
  },
  friendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
  },
  friendName: {
    flex: 1,
    color: theme.colors.neutralWhite,
  },
  followBtn: {
    minWidth: 84,
    borderRadius: 9999,
    height: 38,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 38,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingFooter: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  emptyContainer: {
    alignItems: 'center',
  },
}));

export default YourReferralScreen;
