import { Image, ImageProps, ImageSource } from 'expo-image';
import Animated from 'react-native-reanimated';

type Props = {} & ImageProps;

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export const ExpoImage = (props: Props) => {
  if (!props.source) {
    return null;
  }

  const recyclingKey = (props.source as ImageSource)?.uri?.toString()
    ? (props.source as ImageSource)?.uri?.toString()
    : props.source?.toString();

  return (
    <Image
      placeholder={{ blurhash }}
      recyclingKey={recyclingKey}
      transition={200}
      cachePolicy='disk'
      key={recyclingKey}
      {...props}
    />
  );
};

export const ExpoImageAnimated = Animated.createAnimatedComponent(ExpoImage);
